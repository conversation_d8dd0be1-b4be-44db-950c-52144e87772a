# Audio Dashboard

A comprehensive browser-based audio control panel built with modern web technologies. Features real-time audio monitoring, device selection, spectrum analysis, and professional-grade controls.

## Features

### 🎵 Audio Monitoring & Control
- **Real-time Audio Meters**: Visual level monitoring with peak and RMS display
- **Device Selection**: Choose from available input/output audio devices
- **Gain Controls**: Precise input/output gain adjustment with dB readouts
- **Mute/Unmute**: Quick audio muting capabilities

### 📊 Analysis Tools
- **Spectrum Analyzer**: Real-time frequency domain visualization
- **Multiple Meter Views**: Horizontal and vertical level meters
- **Peak Hold**: Automatic peak level tracking
- **Clipping Detection**: Visual and numerical clipping indicators

### ⚙️ Professional Features
- **Modern Web Audio API**: Uses latest browser audio capabilities
- **No Deprecated Methods**: Built with current web standards
- **Low Latency**: Optimized for real-time audio processing
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **TypeScript**: Type-safe development
- **SvelteKit with Runes Mode**: Modern reactive framework using Svelte 5 runes
- **TailwindCSS v4.x**: Utility-first styling with modern @theme syntax
- **pnpm**: Fast package management
- **Vite 6**: Latest build tool with improved performance
- **Web Audio API**: Browser-native audio processing
- **@lucide/svelte**: Runes-compatible icon library

## Getting Started

### Prerequisites
- Node.js 18+ 
- pnpm package manager
- Modern web browser with Web Audio API support

### Installation

1. Navigate to the project directory:
   ```bash
   cd /Users/<USER>/Projects/audio-dash-1
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Start the development server:
   ```bash
   pnpm dev
   ```

4. Open your browser to `http://localhost:5173`

### Building for Production

```bash
pnpm build
```

### Testing

```bash
pnpm test
```

## Usage

1. **Grant Permissions**: Click "Grant Permissions" to allow microphone access
2. **Select Devices**: Choose your preferred input/output devices
3. **Start Monitoring**: Click "Start Monitoring" to begin audio analysis
4. **Adjust Controls**: Use gain controls and monitor levels in real-time
5. **View Analysis**: Watch the spectrum analyzer for frequency content

## Browser Compatibility

- Chrome/Chromium 66+
- Firefox 60+
- Safari 14.1+
- Edge 79+

**Note**: Requires HTTPS in production for microphone access.

## Project Structure

```
src/
├── lib/
│   ├── components/          # Reusable Svelte components
│   │   ├── AudioMeter.svelte
│   │   ├── DeviceSelector.svelte
│   │   ├── VolumeControl.svelte
│   │   └── SpectrumAnalyzer.svelte
│   └── audioManager.ts      # Core audio management
├── routes/
│   ├── +layout.svelte       # App layout
│   └── +page.svelte         # Main dashboard
├── app.css                  # Global styles
└── app.html                 # HTML template
```

## Configuration

### Audio Settings
- **Sample Rate**: 44.1kHz, 48kHz, 96kHz
- **Buffer Size**: 128-1024 samples
- **Gain Range**: 0-200% (0-2x)

### Visual Settings
- **Meter Orientations**: Horizontal/Vertical
- **Spectrum Scale**: Linear/Logarithmic
- **Color Themes**: Professional dark theme

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## Support

For issues and questions, please open an issue on the project repository.
