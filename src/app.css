@import 'tailwindcss';

@theme {
	/* Dark mode colors (default/base) */
	--color-audio-bg: #0f0f0f;
	--color-audio-surface: #1a1a1a;
	--color-audio-border: #333333;
	--color-audio-text: #ffffff;
	--color-audio-accent: #00ff88;
	--color-audio-warning: #ff4444;
	--color-audio-muted: #888888;

	/* Light mode colors */
	--color-audio-bg-light: #ffffff;
	--color-audio-surface-light: #f8f9fa;
	--color-audio-border-light: #e9ecef;
	--color-audio-text-light: #1a1a1a;
	--color-audio-accent-light: #00b366;
	--color-audio-warning-light: #dc3545;
	--color-audio-muted-light: #6c757d;
}

/* Custom scrollbar styles for dark mode (default) */
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: var(--color-audio-surface);
}

::-webkit-scrollbar-thumb {
	background: var(--color-audio-border);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--color-audio-muted);
}

/* Light mode scrollbar */
.light ::-webkit-scrollbar-track {
	background: var(--color-audio-surface-light);
}

.light ::-webkit-scrollbar-thumb {
	background: var(--color-audio-border-light);
}

.light ::-webkit-scrollbar-thumb:hover {
	background: var(--color-audio-muted-light);
}

/* Custom audio meter styles */
.audio-meter {
	background: linear-gradient(
		to right,
		rgb(34 197 94) 0%,
		rgb(74 222 128) 50%,
		rgb(250 204 21) 75%,
		rgb(239 68 68) 90%,
		rgb(220 38 38) 100%
	);
}

.audio-meter-bg {
	background: var(--color-audio-surface);
	border: 1px solid var(--color-audio-border);
}

.light .audio-meter-bg {
	background: var(--color-audio-surface-light);
	border: 1px solid var(--color-audio-border-light);
}

/* VU Meter specific styles */
.vu-meter {
	background: radial-gradient(
		circle at center,
		var(--color-audio-surface) 0%,
		var(--color-audio-bg) 100%
	);
}

.light .vu-meter {
	background: radial-gradient(
		circle at center,
		var(--color-audio-surface-light) 0%,
		var(--color-audio-bg-light) 100%
	);
}

/* Slider customization for dark mode (default) */
input[type="range"] {
	-webkit-appearance: none;
	appearance: none;
	background: transparent;
	cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
	background: var(--color-audio-border);
	height: 4px;
	border-radius: 2px;
}

input[type="range"]::-webkit-slider-thumb {
	-webkit-appearance: none;
	appearance: none;
	background: var(--color-audio-accent);
	height: 16px;
	width: 16px;
	border-radius: 50%;
	cursor: pointer;
}

input[type="range"]::-moz-range-track {
	background: var(--color-audio-border);
	height: 4px;
	border-radius: 2px;
	border: none;
}

input[type="range"]::-moz-range-thumb {
	background: var(--color-audio-accent);
	height: 16px;
	width: 16px;
	border-radius: 50%;
	cursor: pointer;
	border: none;
}

/* Light mode slider styles */
.light input[type="range"]::-webkit-slider-track {
	background: var(--color-audio-border-light);
}

.light input[type="range"]::-webkit-slider-thumb {
	background: var(--color-audio-accent-light);
}

.light input[type="range"]::-moz-range-track {
	background: var(--color-audio-border-light);
}

.light input[type="range"]::-moz-range-thumb {
	background: var(--color-audio-accent-light);
}

