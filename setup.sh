#!/bin/bash

# Audio Dashboard Setup Script
echo "🎵 Audio Dashboard Setup"
echo "========================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

# Run type checking
echo "🔍 Running type checking..."
pnpm check

# Build the project to verify everything works
echo "🏗️  Building project..."
pnpm build

echo ""
echo "✅ Setup complete!"
echo ""
echo "🚀 To start development:"
echo "   pnpm dev"
echo ""
echo "🌐 To start with network access:"
echo "   pnpm dev --host"
echo ""
echo "📝 Features:"
echo "   • Real-time audio monitoring"
echo "   • Device selection (input/output)"
echo "   • Gain controls with dB readouts"
echo "   • Spectrum analyzer"
echo "   • Professional dark theme"
echo "   • Modern Web Audio API"
echo "   • Svelte 5 runes mode"
echo "   • Vite 6 build system"
echo "   • @lucide/svelte icons"
echo ""
echo "⚠️  Note: HTTPS required for microphone access in production"
echo ""
