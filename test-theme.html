<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Dark mode colors (default/base)
                        'audio-bg': '#0f0f0f',
                        'audio-surface': '#1a1a1a',
                        'audio-border': '#333333',
                        'audio-text': '#ffffff',
                        'audio-accent': '#00ff88',
                        'audio-warning': '#ff4444',
                        'audio-muted': '#888888',
                        // Light mode colors
                        'audio-bg-light': '#ffffff',
                        'audio-surface-light': '#f8f9fa',
                        'audio-border-light': '#e9ecef',
                        'audio-text-light': '#1a1a1a',
                        'audio-accent-light': '#00b366',
                        'audio-warning-light': '#dc3545',
                        'audio-muted-light': '#6c757d'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-audio-bg light:bg-audio-bg-light text-audio-text light:text-audio-text-light transition-colors p-8">
    <div class="max-w-md mx-auto">
        <h1 class="text-2xl font-bold mb-4">Theme Test</h1>
        
        <div class="bg-audio-surface light:bg-audio-surface-light border border-audio-border light:border-audio-border-light p-6 rounded-lg mb-4">
            <h2 class="text-lg font-semibold mb-2">Test Card</h2>
            <p class="text-audio-muted light:text-audio-muted-light mb-4">This card should change colors when you switch themes.</p>
            
            <button id="themeBtn" class="bg-audio-accent light:bg-audio-accent-light text-black px-4 py-2 rounded transition-colors">
                Switch Theme
            </button>
        </div>
        
        <div class="text-sm text-audio-muted light:text-audio-muted-light">
            Current theme: <span id="currentTheme">dark</span>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const currentThemeSpan = document.getElementById('currentTheme');
            
            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                html.classList.add('light');
                currentThemeSpan.textContent = 'light';
            } else {
                html.classList.remove('light');
                html.classList.add('dark');
                currentThemeSpan.textContent = 'dark';
            }
            
            console.log('HTML classes:', html.className);
        }
        
        document.getElementById('themeBtn').addEventListener('click', toggleTheme);
    </script>
</body>
</html>