# Development Guide

## Quick Start

1. **Install dependencies:**
   ```bash
   pnpm install
   ```

2. **Start development server:**
   ```bash
   pnpm dev --host
   ```

3. **Open browser to:**
   - Local: http://localhost:5173
   - Network: http://[your-ip]:5173

## Development Commands

| Command | Description |
|---------|-------------|
| `pnpm dev` | Start development server |
| `pnpm build` | Build for production |
| `pnpm check` | Type checking and linting |
| `pnpm format` | Format code with Prettier |
| `pnpm lint` | Run ESLint |
| `pnpm preview` | Preview production build |

## Project Structure

```
src/
├── lib/
│   ├── components/          # Svelte components (using runes mode)
│   │   ├── AudioMeter.svelte      # Level meters with peak hold
│   │   ├── DeviceSelector.svelte   # Audio device selection
│   │   ├── VolumeControl.svelte    # Gain controls with dB display
│   │   └── SpectrumAnalyzer.svelte # Real-time frequency analysis
│   └── audioManager.ts      # Core Web Audio API wrapper
├── routes/
│   ├── +layout.svelte       # App layout and styles
│   └── +page.svelte         # Main dashboard
├── app.css                  # Global styles and themes
└── app.html                 # HTML template
```

## Audio Features

### AudioManager Class
- Handles Web Audio API initialization
- Device enumeration and selection
- Real-time audio analysis
- Gain control and metering
- Event-based architecture

### Components
- **AudioMeter**: Visual level meters with peak hold and clipping detection
- **DeviceSelector**: Dropdown for input/output device selection
- **VolumeControl**: Gain sliders with percentage, dB, and multiplier display
- **SpectrumAnalyzer**: Real-time frequency domain visualization

## Browser Requirements

- **Chrome/Chromium**: 66+
- **Firefox**: 60+
- **Safari**: 14.1+
- **Edge**: 79+

**Note**: HTTPS required for microphone access in production environments.

## Customization

### Theme Colors
Edit `src/app.css` to modify the color scheme:

```css
@theme {
  --color-audio-bg: #0f0f0f;        /* Background */
  --color-audio-surface: #1a1a1a;    /* Card backgrounds */
  --color-audio-border: #333333;     /* Borders */
  --color-audio-text: #ffffff;       /* Text */
  --color-audio-accent: #00ff88;     /* Accent color */
  --color-audio-warning: #ff4444;    /* Warning/error */
  --color-audio-muted: #888888;      /* Muted text */
}
```

### Audio Settings
Modify default settings in `audioManager.ts`:

```typescript
private settings: AudioSettings = {
  inputDeviceId: 'default',
  outputDeviceId: 'default',
  inputGain: 1.0,           // 0-2 range (200% max)
  outputGain: 1.0,
  muted: false,
  sampleRate: 48000,        // 44100, 48000, 96000
  bufferSize: 256           // 128, 256, 512, 1024
};
```

## Troubleshooting

### Common Issues

1. **Microphone permissions denied**
   - Click "Grant Permissions" button
   - Check browser settings for microphone access
   - Ensure HTTPS in production

2. **No audio devices found**
   - Check system audio settings
   - Refresh the page
   - Try a different browser

3. **Svelte 5 Runes Mode**
   - This project uses Svelte 5 runes mode for modern reactivity
   - All components use `$state`, `$derived`, and `$effect` runes
   - Icons provided by @lucide/svelte which is fully runes-compatible

4. **Vite 6 Compatibility**
   - Updated to Vite 6 for improved performance and features
   - All plugins updated to support Vite 6
   - Faster build times and better HMR

5. **Audio not working**
   - Check browser console for errors
   - Verify Web Audio API support
   - Test with different devices

### Performance Tips

- Use lower buffer sizes (128-256) for lower latency
- Higher sample rates require more CPU
- Spectrum analyzer can be CPU intensive

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run `pnpm check` and `pnpm build`
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
