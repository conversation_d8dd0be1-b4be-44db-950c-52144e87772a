{"name": "audio-dash-1", "version": "0.0.1", "private": true, "scripts": {"build": "vite build", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "dev": "vite dev --host", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "preview": "vite preview", "test": "vitest"}, "devDependencies": {"@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/vite": "^4.0.0-alpha.20", "@types/eslint": "^9.6.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0-alpha.20", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vite": "^6.3.5", "vitest": "^2.0.0"}, "type": "module", "dependencies": {"@lucide/svelte": "^0.522.0", "mode-watcher": "^1.0.8"}}